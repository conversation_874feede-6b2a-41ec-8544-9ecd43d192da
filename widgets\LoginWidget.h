#ifndef LOGINWIDGET_H
#define LOGINWIDGET_H

#include "protocol/XcubeProtoJsonParser.h"
#include "settings/SettingsDialog.h"
#include "tools/ConfigHelper.h"
#include "tools/HttpRequestHelper.h"
#include <QWidget>

namespace Ui {
class LoginWidget;
}

class SystemCenterManagerWidget;
class LoginWidget : public QWidget {
    Q_OBJECT

public:
    explicit LoginWidget(QWidget *parent = nullptr);
    ~LoginWidget();

    void initUi();

Q_SIGNALS:
    void sigLoginSuccess();

private Q_SLOTS:
    void on_pushButtonLogin_clicked();
    void on_pushButtonLogout_clicked();
    void on_pushButtonSetting_clicked();
public Q_SLOTS:
    void on_pushButtonVerifyCode_clicked();

private:
    Ui::LoginWidget *ui;
    ConfigHelper *mConfigHelper;
    HttpRequestHelper *mHttpRequestHelper;
    XcubeProtoJsonParser *mXcubeProtoJsonParser;

    LoginInfo mLoginInfo;
    SettingsDialog *mSettingsDialog;
    VerifyCodeInfo mVerifyCodeInfo;
    SystemCenterManagerWidget *mSystemCenterManagerWidget;
};

#endif // LOGINWIDGET_H
