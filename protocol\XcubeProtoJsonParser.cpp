#include "XcubeProtoJsonParser.h"
#include <QDebug>

XcubeProtoJsonParser::XcubeProtoJsonParser(QObject *parent) : QObject(parent)
{
}

XcubeProtoJsonParser *XcubeProtoJsonParser::mXcubeProtoJsonParser = nullptr;

XcubeProtoJsonParser *XcubeProtoJsonParser::getInstance()
{
    if (nullptr == mXcubeProtoJsonParser) {
        mXcubeProtoJsonParser = new XcubeProtoJsonParser();
    }
    return mXcubeProtoJsonParser;
}

ParseInfo XcubeProtoJsonParser::parserLogin(const QString jsonString)
{
    ParseInfo ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("code")) {
        ret.mCode = jsonObject["code"].toInt();
        qDebug() << "ret.mCode:" << ret.mCode;
    }

    if (jsonObject.contains("data")) {
        QJsonObject dataObject = jsonObject["data"].toObject();

        if (dataObject.contains("username")) {
            ret.mLoginInfo.mUserName = dataObject["username"].toString();
            qDebug() << "ret.mLoginName:" << ret.mLoginInfo.mUserName;
        }

        if (dataObject.contains("license_status")) {
            ret.mLoginInfo.mLicenseStatus = dataObject["license_status"].toString();
            qDebug() << "ret.mLoginInfo.mLicenseStatus:" << ret.mLoginInfo.mLicenseStatus;
        }

        if (dataObject.contains("license_days_left")) {
            ret.mLoginInfo.mLicenseDaysLeft = dataObject["license_days_left"].toInt();
            qDebug() << "ret.mLoginInfo.mLicenseDaysLeft:" << ret.mLoginInfo.mLicenseDaysLeft;
        }
    }

    return ret;
}

ParseInfo XcubeProtoJsonParser::parserVerifyCode(const QString jsonString)
{
    ParseInfo ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("code")) {
        ret.mCode = jsonObject["code"].toInt();
        qDebug() << "ret.mCode:" << ret.mCode;
    }

    if (jsonObject.contains("data")) {
        QJsonObject dataObject = jsonObject["data"].toObject();

        if (dataObject.contains("captcha_id")) {
            ret.mVerifyCodeInfo.mCaptchaId = dataObject["captcha_id"].toString();
            // qDebug()<<"ret.captcha_id:"<<ret.mCaptchaId;
        }

        if (dataObject.contains("captcha_img")) {
            ret.mVerifyCodeInfo.mCaptchaImg = dataObject["captcha_img"].toString();
            // qDebug()<<"ret.captcha_img:"<<ret.mCaptchaImg;
        }
    }

    return ret;
}

ParseInfo XcubeProtoJsonParser::parserResourceInfo(const QString jsonString)
{
    ParseInfo ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("code")) {
        ret.mCode = jsonObject["code"].toInt();
        qDebug() << "ret.mCode:" << ret.mCode;
    }

    if (jsonObject.contains("data")) {
        QJsonArray jsonArray = jsonObject["data"].toArray();
        for (int i = 0; i < jsonArray.size(); ++i) {
            QJsonObject valueObject = jsonArray[i].toObject();
            ResourceInfo resourceInfo;

            if (valueObject.contains("vmid")) {
                resourceInfo.mVmId = valueObject["vmid"].toInt();

                if (valueObject.contains("name")) {
                    resourceInfo.mName = valueObject["name"].toString();
                    // qDebug()<<"resourceInfo.mName:"<<resourceInfo.mName;
                }

                if (valueObject.contains("status")) {
                    resourceInfo.mStatus = valueObject["status"].toString();
                    // qDebug()<<"resourceInfo.mStatus:"<<resourceInfo.mStatus;
                }

                if (valueObject.contains("node")) {
                    resourceInfo.mNode = valueObject["node"].toString();
                    // qDebug()<<"resourceInfo.mNode:"<<resourceInfo.mNode;
                }

                if (valueObject.contains("maxcpu")) {
                    resourceInfo.mMaxCpu = valueObject["cpus"].toInt();
                    // qDebug()<<"resourceInfo.mMaxCpu:"<<resourceInfo.mMaxCpu;
                }

                if (valueObject.contains("maxmem")) {
                    resourceInfo.mMaxMem = (valueObject["memory"].toInt()) / 1024 / 1024 / 1024;
                    // qDebug()<<"resourceInfo.mMaxMem:"<<resourceInfo.mMaxMem;
                }

                if (valueObject.contains("maxdisk")) {
                    resourceInfo.mMaxDisk = (valueObject["disk"].toInt()) / 1024 / 1024 / 1024;
                    // qDebug()<<"resourceInfo.mMaxDisk:"<<resourceInfo.mMaxDisk;
                }

                ret.mResourceInfoMap.insert(resourceInfo.mVmId, resourceInfo);
            }
        }
    }
    return ret;
}

SpiceInfo XcubeProtoJsonParser::parserSpiceInfo(const QString jsonString)
{
    SpiceInfo ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("data")) {
        QJsonObject dataObject = jsonObject["data"].toObject();

        if (dataObject.contains("port")) {
            ret.mSpicePort = dataObject["port"].toInt();
        }

        if (dataObject.contains("password")) {
            ret.mSpicePass = dataObject["password"].toString();
        }
    }

    return ret;
}

QMap<QString, QString> XcubeProtoJsonParser::parserDeviceInfo(const QString jsonString)
{
    QMap<QString, QString> ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("data")) {
        QJsonArray jsonArray = jsonObject["data"].toArray();
        for (int i = 0; i < jsonArray.size(); ++i) {
            QJsonObject valueObject = jsonArray[i].toObject();
            if (valueObject.contains("key") && valueObject.contains("value")) {
                ret.insert(valueObject["key"].toString(), valueObject["value"].toString());
            }
        }
    }

    return ret;
}

QList<GpuInfo> XcubeProtoJsonParser::parserGpuInfo(const QString jsonString)
{
    QList<GpuInfo> ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("data")) {
        QJsonArray dataArray = jsonObject["data"].toArray();
        for (int i = 0; i < dataArray.size(); ++i) {
            QJsonObject gpuListObject = dataArray[i].toObject();
            if (gpuListObject.contains("gpulist")) {
                QJsonArray gpuListArray = gpuListObject["gpulist"].toArray();
                for (int i = 0; i < gpuListArray.size(); ++i) {
                    QJsonObject gpuObject = gpuListArray[i].toObject();
                    GpuInfo gpuInfo;

                    if (gpuObject.contains("vendor_name")) {
                        gpuInfo.mVendorName = gpuObject["vendor_name"].toString();
                    }

                    if (gpuObject.contains("hostname")) {
                        gpuInfo.mHostname = gpuObject["hostname"].toString();
                    }

                    if (gpuObject.contains("device_name")) {
                        gpuInfo.mDeviceName = gpuObject["device_name"].toString();
                    }

                    if (gpuObject.contains("id")) {
                        gpuInfo.mId = gpuObject["id"].toString();
                    }

                    if (gpuObject.contains("vmids") && gpuObject["vmids"].isArray()) {
                        QJsonArray vmidsArray = gpuObject["vmids"].toArray();
                        for (int i = 0; i < vmidsArray.size(); ++i) {
                            QJsonObject vmidsValueObject = vmidsArray[i].toObject();
                            GpuBindVMInfo gpuBindVMInfo;
                            if (vmidsValueObject.contains("vmid")) {
                                gpuBindVMInfo.mVmId = vmidsValueObject["vmid"].toString().toInt();
                            }
                            gpuInfo.mBindVMInfoList.append(gpuBindVMInfo);
                        }
                    }
                    ret.append(gpuInfo);
                }
            }
        }
    }

    return ret;
}

ClusterStatus XcubeProtoJsonParser::parserClusterStatus(const QString jsonString)
{
    ClusterStatus ret;

    QJsonDocument document = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!document.isObject()) {
        qDebug() << "JSON document is not an object";
        return ret;
    }

    QJsonObject jsonObject = document.object();
    if (jsonObject.contains("data")) {
        QJsonArray dataArray = jsonObject["data"].toArray();
        for (int i = 0; i < dataArray.size(); ++i) {
            QJsonObject nodeObject = dataArray[i].toObject();
            if (nodeObject.contains("type")) {
                QString type = nodeObject["type"].toString();
                if ("cluster" == type) {
                    if (nodeObject.contains("version")) {
                        ret.mVersion = nodeObject["version"].toInt();
                    }

                    if (nodeObject.contains("id")) {
                        ret.mId = nodeObject["id"].toString();
                    }

                    if (nodeObject.contains("name")) {
                        ret.mName = nodeObject["name"].toString();
                    }

                    if (nodeObject.contains("nodes")) {
                        ret.mNodes = nodeObject["nodes"].toInt();
                    }

                    if (nodeObject.contains("quorate")) {
                        ret.mQuorate = nodeObject["quorate"].toInt();
                    }
                } else if ("node" == type) {
                    NodeInfo nodeInfo;
                    if (nodeObject.contains("name")) {
                        nodeInfo.mNodeName = nodeObject["name"].toString();
                    }

                    if (nodeObject.contains("ip")) {
                        nodeInfo.mIp = nodeObject["ip"].toString();
                    }

                    if (nodeObject.contains("id")) {
                        nodeInfo.mId = nodeObject["id"].toString();
                    }

                    if (nodeObject.contains("online")) {
                        nodeInfo.mOnline = nodeObject["online"].toInt();
                    }

                    if (nodeObject.contains("nodeid")) {
                        nodeInfo.mNodeId = nodeObject["nodeid"].toInt();
                    }

                    if (nodeObject.contains("level")) {
                        nodeInfo.mLevel = nodeObject["level"].toString();
                    }

                    if (nodeObject.contains("local")) {
                        nodeInfo.mLocal = nodeObject["local"].toInt();
                    }
                    ret.mNodeInfoMap.insert(nodeInfo.mNodeName, nodeInfo);
                }
            }
        }
    }
    return ret;
}
