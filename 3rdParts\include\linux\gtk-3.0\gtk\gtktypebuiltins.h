
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

#ifndef __GTK_TYPE_BUILTINS_H__
#define __GTK_TYPE_BUILTINS_H__

#if !defined (__GTK_H_INSIDE__) && !defined (GTK_COMPILATION)
#error "Only <gtk/gtk.h> can be included directly."
#endif

#include <glib-object.h>
#include <gdk/gdk.h>

G_BEGIN_DECLS

/* enumerations from "gtkrc.h" */
GDK_AVAILABLE_IN_ALL GType gtk_rc_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RC_FLAGS (gtk_rc_flags_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_rc_token_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RC_TOKEN_TYPE (gtk_rc_token_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_path_priority_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PATH_PRIORITY_TYPE (gtk_path_priority_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_path_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PATH_TYPE (gtk_path_type_get_type ())

/* enumerations from "gtkstyle.h" */
GDK_AVAILABLE_IN_ALL GType gtk_expander_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_EXPANDER_STYLE (gtk_expander_style_get_type ())

/* enumerations from "gtktable.h" */
GDK_AVAILABLE_IN_ALL GType gtk_attach_options_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ATTACH_OPTIONS (gtk_attach_options_get_type ())

/* enumerations from "gtkuimanager.h" */
GDK_AVAILABLE_IN_ALL GType gtk_ui_manager_item_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_UI_MANAGER_ITEM_TYPE (gtk_ui_manager_item_type_get_type ())

/* enumerations from "gtkaboutdialog.h" */
GDK_AVAILABLE_IN_ALL GType gtk_license_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_LICENSE (gtk_license_get_type ())

/* enumerations from "gtkaccelgroup.h" */
GDK_AVAILABLE_IN_ALL GType gtk_accel_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ACCEL_FLAGS (gtk_accel_flags_get_type ())

/* enumerations from "gtkapplication.h" */
GDK_AVAILABLE_IN_ALL GType gtk_application_inhibit_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_APPLICATION_INHIBIT_FLAGS (gtk_application_inhibit_flags_get_type ())

/* enumerations from "gtkassistant.h" */
GDK_AVAILABLE_IN_ALL GType gtk_assistant_page_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ASSISTANT_PAGE_TYPE (gtk_assistant_page_type_get_type ())

/* enumerations from "gtkbbox.h" */
GDK_AVAILABLE_IN_ALL GType gtk_button_box_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BUTTON_BOX_STYLE (gtk_button_box_style_get_type ())

/* enumerations from "gtkbuilder.h" */
GDK_AVAILABLE_IN_ALL GType gtk_builder_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BUILDER_ERROR (gtk_builder_error_get_type ())

/* enumerations from "gtkcalendar.h" */
GDK_AVAILABLE_IN_ALL GType gtk_calendar_display_options_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CALENDAR_DISPLAY_OPTIONS (gtk_calendar_display_options_get_type ())

/* enumerations from "gtkcellrenderer.h" */
GDK_AVAILABLE_IN_ALL GType gtk_cell_renderer_state_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CELL_RENDERER_STATE (gtk_cell_renderer_state_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_cell_renderer_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CELL_RENDERER_MODE (gtk_cell_renderer_mode_get_type ())

/* enumerations from "gtkcellrendereraccel.h" */
GDK_AVAILABLE_IN_ALL GType gtk_cell_renderer_accel_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CELL_RENDERER_ACCEL_MODE (gtk_cell_renderer_accel_mode_get_type ())

/* enumerations from "gtkcontainer.h" */
GDK_AVAILABLE_IN_ALL GType gtk_resize_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RESIZE_MODE (gtk_resize_mode_get_type ())

/* enumerations from "gtkcssprovider.h" */
GDK_AVAILABLE_IN_ALL GType gtk_css_provider_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CSS_PROVIDER_ERROR (gtk_css_provider_error_get_type ())

/* enumerations from "gtkcsssection.h" */
GDK_AVAILABLE_IN_ALL GType gtk_css_section_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CSS_SECTION_TYPE (gtk_css_section_type_get_type ())

/* enumerations from "gtkdebug.h" */
GDK_AVAILABLE_IN_ALL GType gtk_debug_flag_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DEBUG_FLAG (gtk_debug_flag_get_type ())

/* enumerations from "gtkdialog.h" */
GDK_AVAILABLE_IN_ALL GType gtk_dialog_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DIALOG_FLAGS (gtk_dialog_flags_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_response_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RESPONSE_TYPE (gtk_response_type_get_type ())

/* enumerations from "gtkdragdest.h" */
GDK_AVAILABLE_IN_ALL GType gtk_dest_defaults_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DEST_DEFAULTS (gtk_dest_defaults_get_type ())

/* enumerations from "gtkentry.h" */
GDK_AVAILABLE_IN_ALL GType gtk_entry_icon_position_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ENTRY_ICON_POSITION (gtk_entry_icon_position_get_type ())

/* enumerations from "gtkenums.h" */
GDK_AVAILABLE_IN_ALL GType gtk_align_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ALIGN (gtk_align_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_arrow_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ARROW_TYPE (gtk_arrow_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_baseline_position_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BASELINE_POSITION (gtk_baseline_position_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_delete_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DELETE_TYPE (gtk_delete_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_direction_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DIRECTION_TYPE (gtk_direction_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_icon_size_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ICON_SIZE (gtk_icon_size_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_sensitivity_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SENSITIVITY_TYPE (gtk_sensitivity_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_text_direction_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_DIRECTION (gtk_text_direction_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_justification_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_JUSTIFICATION (gtk_justification_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_menu_direction_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_MENU_DIRECTION_TYPE (gtk_menu_direction_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_message_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_MESSAGE_TYPE (gtk_message_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_movement_step_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_MOVEMENT_STEP (gtk_movement_step_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_scroll_step_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SCROLL_STEP (gtk_scroll_step_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_orientation_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ORIENTATION (gtk_orientation_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_pack_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PACK_TYPE (gtk_pack_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_position_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_POSITION_TYPE (gtk_position_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_relief_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RELIEF_STYLE (gtk_relief_style_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_scroll_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SCROLL_TYPE (gtk_scroll_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_selection_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SELECTION_MODE (gtk_selection_mode_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_shadow_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SHADOW_TYPE (gtk_shadow_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_state_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_STATE_TYPE (gtk_state_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_toolbar_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TOOLBAR_STYLE (gtk_toolbar_style_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_wrap_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_WRAP_MODE (gtk_wrap_mode_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_sort_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SORT_TYPE (gtk_sort_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_im_preedit_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_IM_PREEDIT_STYLE (gtk_im_preedit_style_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_im_status_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_IM_STATUS_STYLE (gtk_im_status_style_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_pack_direction_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PACK_DIRECTION (gtk_pack_direction_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_pages_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_PAGES (gtk_print_pages_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_page_set_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PAGE_SET (gtk_page_set_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_number_up_layout_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_NUMBER_UP_LAYOUT (gtk_number_up_layout_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_page_orientation_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PAGE_ORIENTATION (gtk_page_orientation_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_quality_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_QUALITY (gtk_print_quality_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_duplex_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_DUPLEX (gtk_print_duplex_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_unit_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_UNIT (gtk_unit_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_tree_view_grid_lines_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TREE_VIEW_GRID_LINES (gtk_tree_view_grid_lines_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_drag_result_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_DRAG_RESULT (gtk_drag_result_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_size_group_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SIZE_GROUP_MODE (gtk_size_group_mode_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_size_request_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SIZE_REQUEST_MODE (gtk_size_request_mode_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_scrollable_policy_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SCROLLABLE_POLICY (gtk_scrollable_policy_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_state_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_STATE_FLAGS (gtk_state_flags_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_region_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_REGION_FLAGS (gtk_region_flags_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_junction_sides_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_JUNCTION_SIDES (gtk_junction_sides_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_border_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BORDER_STYLE (gtk_border_style_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_level_bar_mode_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_LEVEL_BAR_MODE (gtk_level_bar_mode_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_input_purpose_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_INPUT_PURPOSE (gtk_input_purpose_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_input_hints_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_INPUT_HINTS (gtk_input_hints_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_propagation_phase_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PROPAGATION_PHASE (gtk_propagation_phase_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_event_sequence_state_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_EVENT_SEQUENCE_STATE (gtk_event_sequence_state_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_pan_direction_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PAN_DIRECTION (gtk_pan_direction_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_popover_constraint_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_POPOVER_CONSTRAINT (gtk_popover_constraint_get_type ())

/* enumerations from "gtkeventcontrollerscroll.h" */
GDK_AVAILABLE_IN_ALL GType gtk_event_controller_scroll_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_EVENT_CONTROLLER_SCROLL_FLAGS (gtk_event_controller_scroll_flags_get_type ())

/* enumerations from "gtkfilechooser.h" */
GDK_AVAILABLE_IN_ALL GType gtk_file_chooser_action_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_FILE_CHOOSER_ACTION (gtk_file_chooser_action_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_file_chooser_confirmation_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_FILE_CHOOSER_CONFIRMATION (gtk_file_chooser_confirmation_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_file_chooser_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_FILE_CHOOSER_ERROR (gtk_file_chooser_error_get_type ())

/* enumerations from "gtkfilefilter.h" */
GDK_AVAILABLE_IN_ALL GType gtk_file_filter_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_FILE_FILTER_FLAGS (gtk_file_filter_flags_get_type ())

/* enumerations from "gtkfontchooser.h" */
GDK_AVAILABLE_IN_ALL GType gtk_font_chooser_level_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_FONT_CHOOSER_LEVEL (gtk_font_chooser_level_get_type ())

/* enumerations from "gtkicontheme.h" */
GDK_AVAILABLE_IN_ALL GType gtk_icon_lookup_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ICON_LOOKUP_FLAGS (gtk_icon_lookup_flags_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_icon_theme_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ICON_THEME_ERROR (gtk_icon_theme_error_get_type ())

/* enumerations from "gtkiconview.h" */
GDK_AVAILABLE_IN_ALL GType gtk_icon_view_drop_position_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ICON_VIEW_DROP_POSITION (gtk_icon_view_drop_position_get_type ())

/* enumerations from "gtkimage.h" */
GDK_AVAILABLE_IN_ALL GType gtk_image_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_IMAGE_TYPE (gtk_image_type_get_type ())

/* enumerations from "gtkmenu.h" */
GDK_AVAILABLE_IN_ALL GType gtk_arrow_placement_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_ARROW_PLACEMENT (gtk_arrow_placement_get_type ())

/* enumerations from "gtkmessagedialog.h" */
GDK_AVAILABLE_IN_ALL GType gtk_buttons_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BUTTONS_TYPE (gtk_buttons_type_get_type ())

/* enumerations from "gtkmodelbutton.h" */
GDK_AVAILABLE_IN_ALL GType gtk_button_role_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_BUTTON_ROLE (gtk_button_role_get_type ())

/* enumerations from "gtknotebook.h" */
GDK_AVAILABLE_IN_ALL GType gtk_notebook_tab_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_NOTEBOOK_TAB (gtk_notebook_tab_get_type ())

/* enumerations from "gtkpadcontroller.h" */
GDK_AVAILABLE_IN_ALL GType gtk_pad_action_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PAD_ACTION_TYPE (gtk_pad_action_type_get_type ())

/* enumerations from "gtkplacessidebar.h" */
GDK_AVAILABLE_IN_ALL GType gtk_places_open_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PLACES_OPEN_FLAGS (gtk_places_open_flags_get_type ())

/* enumerations from "gtkprintoperation.h" */
GDK_AVAILABLE_IN_ALL GType gtk_print_status_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_STATUS (gtk_print_status_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_operation_result_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_OPERATION_RESULT (gtk_print_operation_result_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_operation_action_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_OPERATION_ACTION (gtk_print_operation_action_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_print_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_PRINT_ERROR (gtk_print_error_get_type ())

/* enumerations from "gtkrecentchooser.h" */
GDK_AVAILABLE_IN_ALL GType gtk_recent_sort_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RECENT_SORT_TYPE (gtk_recent_sort_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_recent_chooser_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RECENT_CHOOSER_ERROR (gtk_recent_chooser_error_get_type ())

/* enumerations from "gtkrecentfilter.h" */
GDK_AVAILABLE_IN_ALL GType gtk_recent_filter_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RECENT_FILTER_FLAGS (gtk_recent_filter_flags_get_type ())

/* enumerations from "gtkrecentmanager.h" */
GDK_AVAILABLE_IN_ALL GType gtk_recent_manager_error_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_RECENT_MANAGER_ERROR (gtk_recent_manager_error_get_type ())

/* enumerations from "gtkrevealer.h" */
GDK_AVAILABLE_IN_ALL GType gtk_revealer_transition_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_REVEALER_TRANSITION_TYPE (gtk_revealer_transition_type_get_type ())

/* enumerations from "gtkscrolledwindow.h" */
GDK_AVAILABLE_IN_ALL GType gtk_corner_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_CORNER_TYPE (gtk_corner_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_policy_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_POLICY_TYPE (gtk_policy_type_get_type ())

/* enumerations from "gtkselection.h" */
GDK_AVAILABLE_IN_ALL GType gtk_target_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TARGET_FLAGS (gtk_target_flags_get_type ())

/* enumerations from "gtkshortcutsshortcut.h" */
GDK_AVAILABLE_IN_ALL GType gtk_shortcut_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SHORTCUT_TYPE (gtk_shortcut_type_get_type ())

/* enumerations from "gtkspinbutton.h" */
GDK_AVAILABLE_IN_ALL GType gtk_spin_button_update_policy_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SPIN_BUTTON_UPDATE_POLICY (gtk_spin_button_update_policy_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_spin_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_SPIN_TYPE (gtk_spin_type_get_type ())

/* enumerations from "gtkstack.h" */
GDK_AVAILABLE_IN_ALL GType gtk_stack_transition_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_STACK_TRANSITION_TYPE (gtk_stack_transition_type_get_type ())

/* enumerations from "gtkstylecontext.h" */
GDK_AVAILABLE_IN_ALL GType gtk_style_context_print_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_STYLE_CONTEXT_PRINT_FLAGS (gtk_style_context_print_flags_get_type ())

/* enumerations from "gtktextbuffer.h" */
GDK_AVAILABLE_IN_ALL GType gtk_text_buffer_target_info_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_BUFFER_TARGET_INFO (gtk_text_buffer_target_info_get_type ())

/* enumerations from "gtktextiter.h" */
GDK_AVAILABLE_IN_ALL GType gtk_text_search_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_SEARCH_FLAGS (gtk_text_search_flags_get_type ())

/* enumerations from "gtktextview.h" */
GDK_AVAILABLE_IN_ALL GType gtk_text_window_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_WINDOW_TYPE (gtk_text_window_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_text_view_layer_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_VIEW_LAYER (gtk_text_view_layer_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_text_extend_selection_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TEXT_EXTEND_SELECTION (gtk_text_extend_selection_get_type ())

/* enumerations from "gtktoolbar.h" */
GDK_AVAILABLE_IN_ALL GType gtk_toolbar_space_style_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TOOLBAR_SPACE_STYLE (gtk_toolbar_space_style_get_type ())

/* enumerations from "gtktoolpalette.h" */
GDK_AVAILABLE_IN_ALL GType gtk_tool_palette_drag_targets_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TOOL_PALETTE_DRAG_TARGETS (gtk_tool_palette_drag_targets_get_type ())

/* enumerations from "gtktreemodel.h" */
GDK_AVAILABLE_IN_ALL GType gtk_tree_model_flags_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TREE_MODEL_FLAGS (gtk_tree_model_flags_get_type ())

/* enumerations from "gtktreeview.h" */
GDK_AVAILABLE_IN_ALL GType gtk_tree_view_drop_position_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TREE_VIEW_DROP_POSITION (gtk_tree_view_drop_position_get_type ())

/* enumerations from "gtktreeviewcolumn.h" */
GDK_AVAILABLE_IN_ALL GType gtk_tree_view_column_sizing_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_TREE_VIEW_COLUMN_SIZING (gtk_tree_view_column_sizing_get_type ())

/* enumerations from "gtkwidget.h" */
GDK_AVAILABLE_IN_ALL GType gtk_widget_help_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_WIDGET_HELP_TYPE (gtk_widget_help_type_get_type ())

/* enumerations from "gtkwindow.h" */
GDK_AVAILABLE_IN_ALL GType gtk_window_type_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_WINDOW_TYPE (gtk_window_type_get_type ())
GDK_AVAILABLE_IN_ALL GType gtk_window_position_get_type (void) G_GNUC_CONST;
#define GTK_TYPE_WINDOW_POSITION (gtk_window_position_get_type ())
G_END_DECLS

#endif /* __GTK_TYPE_BUILTINS_H__ */

/* Generated data ends here */

