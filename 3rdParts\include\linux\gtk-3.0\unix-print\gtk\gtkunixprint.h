/* GtkUnixPrint
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.	 See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library. If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef __GTK_UNIX_PRINT_H__
#define __GTK_UNIX_PRINT_H__

#define __GTK_UNIX_PRINT_H_INSIDE__

#include <gtk/gtkpagesetupunixdialog.h>
#include <gtk/gtkprinter.h>
#include <gtk/gtkprintjob.h>
#include <gtk/gtkprintunixdialog.h>

#include <gtk/gtkunixprint-autocleanups.h>

#undef __GTK_UNIX_PRINT_H_INSIDE__

#endif /* __GTK_UNIX_PRINT_H__ */
