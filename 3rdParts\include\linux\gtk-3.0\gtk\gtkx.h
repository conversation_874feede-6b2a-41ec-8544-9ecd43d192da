/* GTK - The GIMP Toolkit
 * Copyright (C) 2011 Red Hat, Inc.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library. If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef __GTK_X_H__
#define __GTK_X_H__

#if defined (GTK_COMPILATION)
#error "<gtk/gtkx.h> must not be included by GTK+ headers."
#endif

#define __GTKX_H_INSIDE__

#include <gtk/gtk.h>

#include <gtk/gtksocket.h>
#include <gtk/gtkplug.h>

#include <gtk/gtkx-autocleanups.h>

#undef __GTKX_H_INSIDE__

#endif /* __GTK_X_H__ */
