/* gdkconfig.h
 *
 * This is a generated file.  Please modify `configure.ac'
 */

#ifndef __GDKCONFIG_H__
#define __GDKCONFIG_H__

#if !defined (__GDK_H_INSIDE__) && !defined (GDK_COMPILATION)
#error "Only <gdk/gdk.h> can be included directly."
#endif

#include <glib.h>

G_BEGIN_DECLS


#define GDK_WINDOWING_X11
#define GDK_WINDOWING_BROADWAY
#define GDK_WINDOWING_WAYLAND

G_END_DECLS

#endif  /* __GDKCONFIG_H__ */
