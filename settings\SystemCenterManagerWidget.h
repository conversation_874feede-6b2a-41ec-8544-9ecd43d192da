#ifndef SYSTEMCENTERMANAGERWIDGET_H
#define SYSTEMCENTERMANAGERWIDGET_H

#include "common/AbstractSettingsUtil.h"

#include <QDebug>
#include <QWidget>

class ConfigHelper;
namespace Ui {
class SystemCenterManagerWidget;
}

class SystemCenterManagerWidget : public AbstractSettingsWidget {
    Q_OBJECT

public:
    explicit SystemCenterManagerWidget(QWidget *parent = nullptr);
    ~SystemCenterManagerWidget();
    void hide();

protected:
    void updateData();
    void init();
    void initFont();
    bool canBeSaved() override;
    bool saveSettings() override;
    bool cancelSettings() override;
    bool restoreFactorySettings() override;
    void showEvent(QShowEvent *event) override;

private slots:
    void onLogoutReply();
    void on_unlockPushButton_clicked();

public Q_SLOTS:
    void refreshVerifyCode();

Q_SIGNALS:
    void sigRefreshVerifyCode();

private:
    Ui::SystemCenterManagerWidget *ui;
    ConfigHelper *mConfigHelper;
    bool mChangeFsdUrlLock;
};

#endif // SYSTEMCENTERMANAGERWIDGET_H
