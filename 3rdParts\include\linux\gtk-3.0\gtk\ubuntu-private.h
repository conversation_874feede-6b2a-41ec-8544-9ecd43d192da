/*
* Copyright 2013 Canonical Ltd.
*
* This program is free software: you can redistribute it and/or modify it
* under the terms of the GNU General Public License version 3, as published
* by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful, but
* WITHOUT ANY WARRANTY; without even the implied warranties of
* MERCHANTABILITY, SATISFACTORY QUALITY, or FITNESS FOR A PARTICULAR
* PURPOSE.  See the GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License along
* with this program.  If not, see <http://www.gnu.org/licenses/>.
*
* Authors: <AUTHORS>
*/

/*
 * Warning: this file is not part of gtk+, but an Ubuntu-specific extension.
 * The API provided here is meant to be used only from Unity.
 *
 * Applications should not use this.
 */

#ifndef __UBUNTU_PRIVATE_H__
#define __UBUNTU_PRIVATE_H__

#include "ubuntumenuitemfactory.h"

#endif
