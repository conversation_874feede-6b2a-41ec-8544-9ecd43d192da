/* Color utilties
 *
 * Copyright (C) 1999 The Free Software Foundation
 *
 * Authors: <AUTHORS>
 *          <PERSON> <<EMAIL>> (cleanup for GTK+)
 *          <PERSON> <<EMAIL>> (cleanup for GTK+)
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library. If not, see <http://www.gnu.org/licenses/>.
 */

/*
 * Modified by the GTK+ Team and others 1997-2000.  See the AUTHORS
 * file for a list of people on the GTK+ Team.  See the ChangeLog
 * files for a list of changes.  These files are distributed with
 * GTK+ at ftp://ftp.gtk.org/pub/gtk/.
 */

#ifndef __GTK_COLOR_UTILS_H__
#define __GTK_COLOR_UTILS_H__

#if !defined (__GTK_H_INSIDE__) && !defined (GTK_COMPILATION)
#error "Only <gtk/gtk.h> can be included directly."
#endif

#include <glib.h>
#include <gdk/gdk.h>

G_BEGIN_DECLS

GDK_AVAILABLE_IN_ALL
void gtk_hsv_to_rgb (gdouble  h, gdouble  s, gdouble  v,
                     gdouble *r, gdouble *g, gdouble *b);
GDK_AVAILABLE_IN_ALL
void gtk_rgb_to_hsv (gdouble  r, gdouble  g, gdouble  b,
                     gdouble *h, gdouble *s, gdouble *v);

G_END_DECLS

#endif /* __GTK_COLOR_UTILS_H__ */
