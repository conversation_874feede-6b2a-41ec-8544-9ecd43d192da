#include "SystemCenterManagerWidget.h"
#include "ui_SystemCenterManagerWidget.h"

#include "styles/SizeConfigManager.h"
#include "tools/ConfigHelper.h"
#include <QMessageBox>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

SystemCenterManagerWidget::SystemCenterManagerWidget(QWidget *parent)
    : AbstractSettingsWidget(parent)
    , ui(new Ui::SystemCenterManagerWidget)
    , mConfigHelper(ConfigHelper::getConfigHelper())
    , mChangeFsdUrlLock(true)
{
    ui->setupUi(this);
    this->init();
    ui->widgetTitle->setHidden(true);
}

SystemCenterManagerWidget::~SystemCenterManagerWidget()
{
    delete ui;
}

void SystemCenterManagerWidget::updateData()
{
    const ConfigFileModel &configModel = mConfigHelper->getConfigFileModel();
    ui->addressLineEdit->setText(configModel.mServerIp);
    ui->portLineEdit->setText(QString::number(configModel.mServerPort));

    ui->addressLineEdit->setEnabled(!mChangeFsdUrlLock);
    ui->portLineEdit->setEnabled(!mChangeFsdUrlLock);
    ui->unlockPushButton->setVisible(mChangeFsdUrlLock);
    ui->widget_2->setVisible(ui->unlockPushButton->isVisible());

    update();
}

void SystemCenterManagerWidget::init()
{
    this->updateData();
    // Input limits
    QRegularExpression express(R"(^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$)");
    ui->addressLineEdit->setValidator(new QRegularExpressionValidator(express, this));
    express.setPattern(R"(^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$)");
    ui->portLineEdit->setValidator(new QRegularExpressionValidator(express, this));
    initFont();
}

void SystemCenterManagerWidget::initFont()
{
    ui->label->setFont(SizeConfigManager::fontSize(FontManager::T12));
    ui->label_2->setFont(SizeConfigManager::fontSize(FontManager::T12));
}

bool SystemCenterManagerWidget::canBeSaved()
{
    if (ui->addressLineEdit->text().isEmpty()) {
        showMessage(tr("The IP address is empty."));
        return false;
    }

    if (ui->portLineEdit->text().isEmpty()) {
        showMessage(tr("The port is empty."));
        return false;
    }

    return true;
}

bool SystemCenterManagerWidget::saveSettings()
{
    ConfigFileModel &configModel = mConfigHelper->getConfigFileModel();
    const QString &modifyIp = ui->addressLineEdit->text();
    const int modifyPort = ui->portLineEdit->text().toInt();

    if (configModel.mServerIp == modifyIp && configModel.mServerPort == modifyPort)
        return true;

    const QString &ip = modifyIp;
    QRegularExpression expressin(R"(^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$)");
    QRegularExpressionMatch match = expressin.match(ip);

    if (!match.hasMatch()) {
        QMessageBox::warning(this, "警告", tr("请输入正确的IP"));
        return false;
    }

    configModel.mServerIp = modifyIp;
    configModel.mServerPort = modifyPort;
    mConfigHelper->saveConfig();
    refreshVerifyCode();

    return true;
}

bool SystemCenterManagerWidget::cancelSettings()
{
    return true;
}

bool SystemCenterManagerWidget::restoreFactorySettings()
{
    return false;
}

void SystemCenterManagerWidget::showEvent(QShowEvent *event)
{
    updateData();
    return QWidget::showEvent(event);
}

void SystemCenterManagerWidget::onLogoutReply()
{
}

void SystemCenterManagerWidget::on_unlockPushButton_clicked()
{
    ui->unlockPushButton->setVisible(false);
    ui->addressLineEdit->setEnabled(true);
    ui->portLineEdit->setEnabled(true);
    ui->widget_2->setVisible(ui->unlockPushButton->isVisible());
    update();
}

void SystemCenterManagerWidget::refreshVerifyCode()
{
    emit sigRefreshVerifyCode();
}
void SystemCenterManagerWidget::hide()
{
    setVisible(false);
}