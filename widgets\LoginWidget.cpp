#include "LoginWidget.h"
#include "styles/SizeConfigManager.h"
#include "tools/ApiUrlManage.h"
#include "ui_LoginWidget.h"
#include <QMessageBox>
#include <QRegularExpressionValidator>

LoginWidget::LoginWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::LoginWidget)
    , mConfigHelper(ConfigHelper::getConfigHelper())
    , mHttpRequestHelper(HttpRequestHelper::getInstance())
    , mXcubeProtoJsonParser(XcubeProtoJsonParser::getInstance())
    , mSettingsDialog(new SettingsDialog(this))
    , mSystemCenterManagerWidget(new SystemCenterManagerWidget(this))
{
    mSystemCenterManagerWidget->hide();
    ui->setupUi(this);

    if (mConfigHelper->getConfigFileModel().mServerIp.isEmpty() ||
            mConfigHelper->getConfigFileModel().mServerPort <= 0) {
        mSettingsDialog->show();
    } else {
        mSettingsDialog->close();
    }
    connect(mSystemCenterManagerWidget, &SystemCenterManagerWidget::sigRefreshVerifyCode, this, &LoginWidget::on_pushButtonVerifyCode_clicked);
    initUi();
}

LoginWidget::~LoginWidget()
{
    delete ui;
    delete mSystemCenterManagerWidget;
}

void LoginWidget::initUi()
{
    ui->lineEditUserName->setText(mConfigHelper->getConfigFileModel().mUserName);
    ui->lineEditUserPass->setText(mConfigHelper->getConfigFileModel().mUserPass);
    ui->lineEditUserName->setFont(SizeConfigManager::fontSize(FontManager::T12));
    ui->lineEditUserPass->setFont(SizeConfigManager::fontSize(FontManager::T12));

    ui->labelUserName->setFont(SizeConfigManager::fontSize(FontManager::T10));
    ui->labelUserPass->setFont(SizeConfigManager::fontSize(FontManager::T10));

    ui->lineEditUserName->setStyleSheet("QLineEdit{border-radius:4px}");
    ui->lineEditUserPass->setStyleSheet("QLineEdit{border-radius:4px}");

    ui->lineEditUserName->setMinimumHeight(30);
    ui->lineEditUserPass->setMinimumHeight(30);

    ui->lineEditVerifyCode->setText("");
    on_pushButtonVerifyCode_clicked();
}

void LoginWidget::on_pushButtonLogin_clicked()
{
    if (ui->lineEditVerifyCode->text().isEmpty()) {
        QMessageBox::warning(this, "错误", "验证码不能为空!");
        return;
    }

    if (ui->lineEditUserName->text().isEmpty() || ui->lineEditUserPass->text().isEmpty()) {
        QMessageBox::warning(this, "错误", "用户名或密码不能为空!");
        return;
    }

    QString postStr = QString("{\"username\": \"%1\",\"password\": "
                              "\"%2\",\"captcha_code\":\"%3\",\"captcha_id\":\"%4\"}")
                              .arg(ui->lineEditUserName->text())
                              .arg(ui->lineEditUserPass->text())
                              .arg(ui->lineEditVerifyCode->text())
                              .arg(mVerifyCodeInfo.mCaptchaId);
    HttpResultInfo resultInfo = mHttpRequestHelper->postMethod(ApiUrlManage::getLoginUrl(), postStr);
    qDebug() << "resultInfo.resultHtml:" << resultInfo.resultHtml;
    if (HttpStatusCode_OK == resultInfo.httpStatusCode) {
        ParseInfo parseInfo = mXcubeProtoJsonParser->parserLogin(resultInfo.resultHtml);
        if (0 != parseInfo.mCode) {
            QMessageBox::warning(this, "错误", "登录报错");
            return;
        }

        mLoginInfo = parseInfo.mLoginInfo;
        mConfigHelper->getConfigFileModel().mUserName = ui->lineEditUserName->text();
        mConfigHelper->getConfigFileModel().mUserPass = ui->lineEditUserPass->text();
        mConfigHelper->saveConfig();

        emit sigLoginSuccess();
    } else {
        QMessageBox::warning(this, "错误", "登录失败");
    }
}

void LoginWidget::on_pushButtonLogout_clicked()
{
    qApp->quit();
}

void LoginWidget::on_pushButtonSetting_clicked()
{
    mSettingsDialog->show();
}

void LoginWidget::on_pushButtonVerifyCode_clicked()
{
    qDebug() << "ApiUrlManage::getVerifyCode():" << ApiUrlManage::getVerifyCode();
    HttpResultInfo resultInfo = mHttpRequestHelper->getMethod(ApiUrlManage::getVerifyCode());
    if (HttpStatusCode_OK == resultInfo.httpStatusCode) {
        ParseInfo parseInfo = mXcubeProtoJsonParser->parserVerifyCode(resultInfo.resultHtml);
        if (0 != parseInfo.mCode) {
            QMessageBox::warning(this, "错误", "获取验证码失败");
            return;
        }
        mVerifyCodeInfo = parseInfo.mVerifyCodeInfo;
        QString imageBase64 = mVerifyCodeInfo.mCaptchaImg.right(
                mVerifyCodeInfo.mCaptchaImg.size() - mVerifyCodeInfo.mCaptchaImg.indexOf(","));
        QByteArray imageBytes = QByteArray::fromBase64(imageBase64.toUtf8());

        QPixmap pixmap;
        if (mVerifyCodeInfo.mCaptchaImg.contains("png", Qt::CaseInsensitive)) {
            pixmap.loadFromData(imageBytes, "PNG");
        } else if (mVerifyCodeInfo.mCaptchaImg.contains("jpg", Qt::CaseInsensitive)) {
            pixmap.loadFromData(imageBytes, "JPG");
        } else {
            qDebug() << "verify code format unknown";
            return;
        }
        pixmap = pixmap.scaled(ui->pushButtonVerifyCode->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
        ui->pushButtonVerifyCode->setIcon(pixmap);
        ui->pushButtonVerifyCode->setIconSize(ui->pushButtonVerifyCode->size());
    }
}
